package com.greenterp

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// 绘制路径数据类
data class DrawingPath(
    val path: Path,
    val color: Color,
    val strokeWidth: Float
)

// 画布数据类（包含绘制路径和偏移量）
data class CanvasData(
    val paths: List<DrawingPath> = emptyList(),
    val offset: Offset = Offset.Zero
)


@Composable
fun DrawingCanvas(
    sessionId: String = "default",
    timestamp: String = "", // 时间戳
    isDrawingMode: Boolean = false, // 全局绘制模式状态
    onDrawingModeChanged: (Boolean) -> Unit = {}, // 绘制模式变化回调
    onLayoutSwapRequested: () -> Unit = {}, // 布局交换回调
    initialCanvasData: CanvasData = CanvasData(), // 初始画布数据
    onCanvasDataChanged: (CanvasData) -> Unit = {}, // 画布数据变化回调
    onBackClick: () -> Unit = {}, // Back按钮回调
    onNextClick: () -> Unit = {}, // Next按钮回调
    modifier: Modifier = Modifier
) {
    var paths by remember(sessionId) { mutableStateOf(initialCanvasData.paths) }
    var canvasOffset by remember(sessionId) { mutableStateOf(initialCanvasData.offset) }
    var currentPath by remember(sessionId) { mutableStateOf(Path()) }
    var currentColor by remember(sessionId) { mutableStateOf(Color.Black) }
    var currentStrokeWidth by remember(sessionId) { mutableStateOf(5f) }
    var showColorPicker by remember(sessionId) { mutableStateOf(false) }
    // 移除内部的isDrawingMode状态，直接使用传入的全局状态
    var isEraserMode by remember(sessionId) { mutableStateOf(false) } // 橡皮擦模式
    var showStrokeWidthDropdown by remember(sessionId) { mutableStateOf(false) } // 笔刷粗细下拉框

    // 优化：使用单独的状态来控制当前路径的重绘，避免频繁重组
    var currentPathRevision by remember(sessionId) { mutableStateOf(0) }

    // 优化：记录上一个触摸点，用于平滑绘制
    var lastTouchPoint by remember(sessionId) { mutableStateOf<Offset?>(null) }



    // 当sessionId变化时，重新初始化状态（仅在sessionId变化时）
    LaunchedEffect(sessionId) {
        paths = initialCanvasData.paths
        canvasOffset = initialCanvasData.offset
        currentPathRevision = 0
        lastTouchPoint = null
    }

    // 更新画布数据的函数
    fun updateCanvasData() {
        val canvasData = CanvasData(paths = paths, offset = canvasOffset)
        println("DrawingCanvas updateCanvasData: sessionId=$sessionId, pathsCount=${paths.size}")
        onCanvasDataChanged(canvasData)
    }

    val density = LocalDensity.current

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
        // 工具栏
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color.White,
            shadowElevation = 2.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧转换按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFFF0F0F0))
                        .clickable {
                            onLayoutSwapRequested()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.exchange),
                        contentDescription = "Swap Layout",
                        tint = Color(0xFF666666),
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 右侧工具按钮组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                // 1. 绘制/浏览模式切换按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            if (isDrawingMode) Color(0xFF07c160) else Color(0xFFF0F0F0)
                        )
                        .clickable {
                            onDrawingModeChanged(!isDrawingMode)
                            if (isDrawingMode) isEraserMode = false
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        if (isDrawingMode) Icons.Default.Edit else Icons.Default.Edit,
                        contentDescription = if (isDrawingMode) "Drawing Mode" else "Canvas Browsing Mode",
                        tint = if (!isDrawingMode) Color(0xFF666666) else Color.White,
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 2. 颜色选择按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(currentColor)
                        .border(1.dp, Color(0xFFE0E0E0), RoundedCornerShape(3.dp))
                        .clickable { showColorPicker = !showColorPicker }
                )

                // 3. 笔刷粗细按钮（改为与其他按钮一致的大小）
                Box {
                    Box(
                        modifier = Modifier
                            .width(54.dp)
                            .height(24.dp)
                            .clip(RoundedCornerShape(3.dp))
                            .background(Color(0xFFF0F0F0))
                            .clickable { showStrokeWidthDropdown = !showStrokeWidthDropdown },
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .width(12.dp)
                                    .height((currentStrokeWidth / 2).dp.coerceAtMost(8.dp))
                                    .background(Color(0xFF666666), RoundedCornerShape((currentStrokeWidth / 4).dp))
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                Icons.Default.ArrowDropDown,
                                contentDescription = "Stroke Width",
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(8.dp)
                            )
                        }
                    }

                    DropdownMenu(
                        expanded = showStrokeWidthDropdown,
                        onDismissRequest = { showStrokeWidthDropdown = false }
                    ) {
                        listOf(2f, 5f, 8f, 12f, 20f).forEach { width ->
                            DropdownMenuItem(
                                text = {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.padding(vertical = 4.dp)
                                    ) {
                                        Box(
                                            modifier = Modifier
                                                .width(40.dp)
                                                .height(width.dp)
                                                .background(Color.Black, RoundedCornerShape(width.dp / 2))
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text("${width.toInt()}px", fontSize = 12.sp)
                                    }
                                },
                                onClick = {
                                    currentStrokeWidth = width
                                    showStrokeWidthDropdown = false
                                }
                            )
                        }
                    }
                }

                // 4. 橡皮擦按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            if (isEraserMode) Color(0xFFFF6B6B) else Color(0xFFF0F0F0)
                        )
                        .clickable {
                            isEraserMode = !isEraserMode
                            if (isEraserMode && !isDrawingMode) {
                                onDrawingModeChanged(true)
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "Eraser",
                        tint = if (isEraserMode) Color.White else Color(0xFF666666),
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 5. 清空画布按钮
                Box(
                    modifier = Modifier
                        .size(22.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFFF0F0F0))
                        .clickable {
                            paths = emptyList()
                            currentPath = Path()
                            updateCanvasData()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Refresh,
                        contentDescription = "Clear Canvas",
                        tint = Color(0xFF666666),
                        modifier = Modifier.size(13.dp)
                    )
                }
                }
            }
        }

        // 时间戳分隔线 - 和语音识别样式一致
        if (timestamp.isNotEmpty()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = 1.dp,
                    color = Color(0xFF07c160)
                )
                Text(
                    text = timestamp,
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = (14 * 1.4f).sp,
                        fontWeight = FontWeight.Medium
                    ),
                    color = Color(0xFF07c160),
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = 1.dp,
                    color = Color(0xFF07c160)
                )
            }
        }

        // 颜色选择器弹窗
        if (showColorPicker) {
            com.greenterp.ui.components.ColorPickerDialog(
                currentColor = currentColor,
                onColorSelected = { selectedColor ->
                    currentColor = selectedColor
                },
                onDismiss = { showColorPicker = false },
                isDarkTheme = false, // 画布颜色选择器使用浅色主题
                title = "Choose Brush Color"
            )
        }

        // 画布区域 - 支持无限拖动
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clip(RectangleShape) // 裁剪画布内容，防止超出边界
                .pointerInput(isDrawingMode, isEraserMode) {
                    if (isDrawingMode) {
                        // 绘制模式：区分手写笔和手指触摸
                        awaitPointerEventScope {
                            while (true) {
                                val event = awaitPointerEvent()
                                val change = event.changes.first()

                                when (event.type) {
                                    PointerEventType.Press -> {
                                        if (change.type == PointerType.Stylus) {
                                            // 手写笔：开始绘制
                                            val adjustedOffset = change.position - canvasOffset
                                            if (isEraserMode) {
                                                // 橡皮擦模式：查找并删除触摸点附近的路径
                                                val newPaths = paths.filter { drawingPath ->
                                                    !isPointNearPath(adjustedOffset, drawingPath.path, drawingPath.strokeWidth)
                                                }
                                                paths = newPaths
                                                updateCanvasData()
                                            } else {
                                                // 绘制模式：创建新的Path对象确保状态变化
                                                currentPath = Path().apply {
                                                    moveTo(adjustedOffset.x, adjustedOffset.y)
                                                }
                                                lastTouchPoint = adjustedOffset
                                                currentPathRevision++
                                            }
                                            change.consume()
                                        }
                                        // 手指触摸不消费事件，让拖动手势处理
                                    }
                                    PointerEventType.Move -> {
                                        if (change.type == PointerType.Stylus && change.pressed) {
                                            // 手写笔移动：继续绘制
                                            val adjustedPosition = change.position - canvasOffset
                                            if (isEraserMode) {
                                                // 橡皮擦模式：继续删除路径
                                                val newPaths = paths.filter { drawingPath ->
                                                    !isPointNearPath(adjustedPosition, drawingPath.path, drawingPath.strokeWidth)
                                                }
                                                if (newPaths.size != paths.size) {
                                                    paths = newPaths
                                                    updateCanvasData()
                                                }
                                            } else {
                                                // 创建新的Path对象来确保状态变化被检测到
                                                val newPath = Path().apply {
                                                    addPath(currentPath)
                                                    lastTouchPoint?.let { lastPoint ->
                                                        // 计算中点，使用二次贝塞尔曲线平滑连接
                                                        val midX = (lastPoint.x + adjustedPosition.x) / 2
                                                        val midY = (lastPoint.y + adjustedPosition.y) / 2
                                                        quadraticBezierTo(
                                                            lastPoint.x, lastPoint.y,
                                                            midX, midY
                                                        )
                                                    } ?: run {
                                                        // 如果没有上一个点，直接连线
                                                        lineTo(adjustedPosition.x, adjustedPosition.y)
                                                    }
                                                }
                                                currentPath = newPath
                                                lastTouchPoint = adjustedPosition
                                                // 立即触发重绘，确保实时响应
                                                currentPathRevision++
                                            }
                                            change.consume()
                                        }
                                    }
                                    PointerEventType.Release -> {
                                        if (change.type == PointerType.Stylus) {
                                            // 手写笔抬起：完成绘制
                                            if (!isEraserMode && !currentPath.isEmpty) {
                                                // 创建新的Path副本保存到paths中
                                                val pathCopy = Path().apply { addPath(currentPath) }
                                                val newPaths = paths + DrawingPath(
                                                    path = pathCopy,
                                                    color = currentColor,
                                                    strokeWidth = currentStrokeWidth
                                                )
                                                paths = newPaths
                                                currentPath.reset()
                                                lastTouchPoint = null
                                                currentPathRevision++
                                                updateCanvasData()
                                            }
                                            change.consume()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .pointerInput(isDrawingMode) {
                    // 手指拖动处理（绘制模式下移动画布，浏览模式下禁用以允许滚动）
                    if (isDrawingMode) {
                        detectDragGestures(
                            onDrag = { change, dragAmount ->
                                // 只有手指触摸才移动画布
                                if (change.type != PointerType.Stylus) {
                                    canvasOffset += dragAmount
                                }
                            },
                            onDragEnd = {
                                // 拖动结束时保存偏移量
                                updateCanvasData()
                            }
                        )
                    }
                }
        ) {
            // 应用画布偏移
            translate(canvasOffset.x, canvasOffset.y) {
                // 绘制所有已完成的路径
                paths.forEach { drawingPath ->
                    drawPath(
                        path = drawingPath.path,
                        color = drawingPath.color,
                        style = Stroke(
                            width = drawingPath.strokeWidth,
                            cap = StrokeCap.Round,
                            join = StrokeJoin.Round
                        )
                    )
                }

                // 绘制当前正在绘制的路径
                // 确保实时显示当前绘制内容
                if (!isEraserMode && !currentPath.isEmpty) {
                    drawPath(
                        path = currentPath,
                        color = currentColor,
                        style = Stroke(
                            width = currentStrokeWidth,
                            cap = StrokeCap.Round,
                            join = StrokeJoin.Round
                        )
                    )
                }
            }
        }
        }

        // 可拖动的Back/Next按钮 - 一直显示
        DraggableNavigationButtons(
            onBackClick = onBackClick,
            onNextClick = onNextClick,
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 固定位置的导航按钮组件
 * Next按钮固定在顶部，Back按钮固定在底部
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
private fun DraggableNavigationButtons(
    onBackClick: () -> Unit = {},
    onNextClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier = modifier) {
        val screenHeight = maxHeight

        // 计算工具栏高度（估算：padding + 内容高度）
        val toolbarHeight = 56.dp  // 工具栏大约高度

        // Next按钮紧靠左上角（工具栏下方）
        FixedNavigationButton(
            text = "Next",
            icon = "arrow_down_with_line", // 自定义箭头样式
            contentDescription = "Next",
            onClick = onNextClick,
            modifier = Modifier
                .offset(x = 0.dp, y = toolbarHeight)
        )

        // Back按钮紧靠左下角
        FixedNavigationButton(
            text = "Back",
            icon = "arrow_up_with_line", // 自定义箭头样式
            contentDescription = "Back",
            onClick = onBackClick,
            modifier = Modifier
                .offset(x = 0.dp, y = screenHeight - 48.dp) // 48dp是按钮高度
        )
    }
}

/**
 * 固定位置的导航按钮，包含文字和自定义箭头
 */
@Composable
private fun FixedNavigationButton(
    text: String,
    icon: String, // 改为字符串标识符
    contentDescription: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = Color.White.copy(alpha = 0.9f),
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = 1.dp,
                color = Color.Gray.copy(alpha = 0.3f),
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 统一布局：文字在左，箭头在右
            Text(
                text = text,
                fontSize = 14.sp,
                color = Color(0xFF666666),
                fontWeight = FontWeight.Medium
            )
            CustomArrowIcon(
                type = icon,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 自定义箭头图标组件
 * 支持带横杠的上下箭头
 */
@Composable
private fun CustomArrowIcon(
    type: String,
    modifier: Modifier = Modifier
) {
    Canvas(modifier = modifier) {
        val strokeWidth = 2.dp.toPx()
        val color = Color(0xFF666666)
        val centerX = size.width / 2
        val centerY = size.height / 2
        val arrowSize = size.width * 0.3f
        val lineLength = size.width * 0.6f

        when (type) {
            "arrow_down_with_line" -> {
                // Next箭头：向下箭头，下面有横杠
                // 绘制向下箭头
                drawLine(
                    color = color,
                    start = Offset(centerX - arrowSize, centerY - arrowSize/2),
                    end = Offset(centerX, centerY + arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                drawLine(
                    color = color,
                    start = Offset(centerX, centerY + arrowSize/2),
                    end = Offset(centerX + arrowSize, centerY - arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                // 绘制下方横杠
                drawLine(
                    color = color,
                    start = Offset(centerX - lineLength/2, centerY + arrowSize),
                    end = Offset(centerX + lineLength/2, centerY + arrowSize),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
            "arrow_up_with_line" -> {
                // Back箭头：向上箭头，上面有横杠
                // 绘制向上箭头
                drawLine(
                    color = color,
                    start = Offset(centerX - arrowSize, centerY + arrowSize/2),
                    end = Offset(centerX, centerY - arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                drawLine(
                    color = color,
                    start = Offset(centerX, centerY - arrowSize/2),
                    end = Offset(centerX + arrowSize, centerY + arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                // 绘制上方横杠
                drawLine(
                    color = color,
                    start = Offset(centerX - lineLength/2, centerY - arrowSize),
                    end = Offset(centerX + lineLength/2, centerY - arrowSize),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
        }
    }
}

// 辅助函数：检查点是否接近路径（用于橡皮擦功能）
private fun isPointNearPath(point: Offset, path: Path, strokeWidth: Float): Boolean {
    // 简化的实现：检查点是否在路径的边界框内
    val pathBounds = path.getBounds()
    val tolerance = strokeWidth / 2 + 10f // 增加一些容差

    return point.x >= pathBounds.left - tolerance &&
            point.x <= pathBounds.right + tolerance &&
            point.y >= pathBounds.top - tolerance &&
            point.y <= pathBounds.bottom + tolerance
}